import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { LanguageEnum } from '@/enums/appEnum'
import { router } from '@/router'
import { UserInfo } from '@/types/store'
import { useSettingStore } from './setting'
import { useWorktabStore } from './worktab'
import { MenuListType } from '@/types/menu'
import { setPageTitle } from '@/router/utils/utils'
import { resetRouterState, resetMenuLoadState } from '@/router/menu-handler'
// 用户
export const useUserStore = defineStore(
  'userStore',
  () => {
    const language = ref(LanguageEnum.ZH)
    const isLogin = ref(false)
    const isLock = ref(false)
    const lockPassword = ref('')
    const info = ref<Partial<UserInfo>>({})
    const searchHistory = ref<MenuListType[]>([])
    const accessToken = ref('')
    const refreshToken = ref('')

    const getUserInfo = computed(() => info.value)
    const getSettingState = computed(() => useSettingStore().$state)
    const getWorktabState = computed(() => useWorktabStore().$state)

    const setUserInfo = (newInfo: UserInfo) => {
      info.value = newInfo
    }

    const setLoginStatus = (status: boolean) => {
      isLogin.value = status
    }

    const setLanguage = (lang: LanguageEnum) => {
      setPageTitle(router.currentRoute.value)
      language.value = lang
    }

    const setSearchHistory = (list: MenuListType[]) => {
      searchHistory.value = list
    }

    const setLockStatus = (status: boolean) => {
      isLock.value = status
    }

    const setLockPassword = (password: string) => {
      lockPassword.value = password
    }

    const setToken = (newAccessToken: string, newRefreshToken?: string) => {
      accessToken.value = newAccessToken
      if (newRefreshToken) {
        refreshToken.value = newRefreshToken
      }
    }

    // 初始化登录状态：如果有token则设置为已登录
    const initializeLoginStatus = () => {
      if (accessToken.value && !isLogin.value) {
        console.log('检测到存储的token，设置登录状态为true')
        isLogin.value = true
      }
    }

    // 监听token变化，自动设置登录状态
    watch(
      accessToken,
      (newToken) => {
        if (newToken && !isLogin.value) {
          console.log('token变化，设置登录状态为true')
          isLogin.value = true
        } else if (!newToken && isLogin.value) {
          console.log('token清空，设置登录状态为false')
          isLogin.value = false
        }
      },
      { immediate: true }
    )

    const logOut = () => {
      info.value = {}
      isLogin.value = false
      isLock.value = false
      lockPassword.value = ''
      accessToken.value = ''
      refreshToken.value = ''
      useWorktabStore().$patch({
        opened: []
      })
      sessionStorage.removeItem('iframeRoutes')
      resetRouterState(router)
      resetMenuLoadState() // 重置菜单加载状态
      router.push('/login').catch((err) => {
        console.error('登录页面跳转失败:', err)
      })
    }

    // 初始化登录状态
    initializeLoginStatus()

    return {
      language,
      isLogin,
      isLock,
      lockPassword,
      info,
      searchHistory,
      accessToken,
      refreshToken,
      getUserInfo,
      getSettingState,
      getWorktabState,
      setUserInfo,
      setLoginStatus,
      setLanguage,
      setSearchHistory,
      setLockStatus,
      setLockPassword,
      setToken,
      logOut,
      initializeLoginStatus
    }
  },
  {
    persist: {
      key: 'user',
      storage: localStorage
    }
  }
)
